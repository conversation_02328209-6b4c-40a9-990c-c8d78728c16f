username: bmhjkl
scoreboard-disabled: false
duel-requests-disabled: false
hide-other-players: false
stats:
  brackets: 0
  kills: 0
  deaths: 0
  lms: 0
  party_vs_party_wins: 0
  global_elo: 1000
  sumo_wins: 0
  juggernaut: 0
  elo_nodebuffelo: 1000
  elo_debuffelo: 1000
  elo_builduhcelo: 1000
  elo_gappleelo: 1000
  elo_soupelo: 1000
  elo_comboelo: 1000
  elo_sumoelo: 1000
  elo_sumobestof3elo: 1000
  elo_nodebuffelopremium: 1000
  elo_hcfelo: 1000
  elo_axepvpelo: 1000
limits-updated: 1753770417611
rankeds-left: 20
unrankeds-left: 0
premium-matches: 0
cooldowns: []
custom-kit:
  ==: BattleKit
  pearl-cooldown: 0
  best-of: 1
  no-bestof-rollback: false
  icon:
    ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: STONE_SWORD
    meta:
      ==: ItemMeta
      meta-type: UNSPECIFIC
      display-name: '{"text":"","extra":[{"text":"Premade CustomKit","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"red","bold":true}]}'
      ItemFlags:
      - HIDE_ENCHANTS
      - HIDE_ATTRIBUTES
      - HIDE_UNBREAKABLE
      - HIDE_DESTROYS
      - HIDE_PLACED_ON
      - HIDE_POTION_EFFECTS
      - HIDE_DYE
      - HIDE_ARMOR_TRIM
  no-damage: false
  inventory:
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: STONE_SWORD
    meta:
      ==: ItemMeta
      meta-type: UNSPECIFIC
      display-name: '{"text":"","extra":[{"text":"Premade CustomKit","obfuscated":false,"italic":false,"underlined":false,"strikethrough":false,"color":"red","bold":true}]}'
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: FISHING_ROD
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: COBBLESTONE
    amount: 64
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: BREAD
    amount: 32
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: GOLDEN_APPLE
    amount: 4
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: ARROW
    amount: 64
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: FLINT_AND_STEEL
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: BOW
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  - ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: AIR
    amount: 0
  shoot-fireballs: false
  kill-regen: false
  horse: true
  no-death-countdown: false
  chestplate:
    ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: IRON_CHESTPLATE
  auto-tnt: false
  build: true
  helmet:
    ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: LEATHER_HELMET
    meta:
      ==: ItemMeta
      meta-type: COLORABLE_ARMOR
      enchants:
        PROTECTION_ENVIRONMENTAL: 2
  name: premadecustomkit
  boots:
    ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: LEATHER_BOOTS
    meta:
      ==: ItemMeta
      meta-type: COLORABLE_ARMOR
      enchants:
        PROTECTION_FALL: 4
        PROTECTION_ENVIRONMENTAL: 2
  boxing: false
  block-disappear: 0
  fireball-jumps: false
  no-fall-damage: false
  leggings:
    ==: org.bukkit.inventory.ItemStack
    v: 3700
    type: GOLDEN_LEGGINGS
  tnt-jumps: false
kits: []
language: English

english:
  yes-or-true: '&6Yes'
  no-or-false: '&cNo'
  surrender: '&fYou have &6surrendered &ffrom the current fight'
  surrender-announce: '&fPlayer &6<player> &fhas surrendered from the fight'
  cooldown-message: '&cYou can''t use that again yet! You must wait <time> seconds'
  do-not-spam-things: '&cDo not spam things!'
  not-pvp-fight: '&cYou can''t hit others in this fight!'
  party-created: '&aYou have created a new party!'
  can-not-duel-that-party: '&cCan not duel that party.'
  playback-can-not-start: '&cPlayback can not be started because the arena is not currently free.'
  in-party: '&aYou are already in a party!'
  data-not-loaded: '&c&lD<PERSON> hasn''t loaded yet! Please wait a moment.'
  ranked-range: '&6Searching for players in elo range (&e<range1>-<range2>&6).'
  ranked-anyone: '&6Searching for players in any elo range.'
  invalid-kit: '&cCan not preview an invalid kit!'
  not-own-party: '&cYou are not an owner of any party!'
  must-own-party: '&cYou must be the owner of this party to do this!'
  party-size: '&c<PERSON><PERSON><PERSON> must have <number> players!'
  stay-away-from-other-players: '&cPlease stay away from other players while spectating!'
  public-party-no-permission: '&cYou do not have permission to host public parties!'
  no-party-limit-bypass: '&cYou don''t have permission to change the player limit of your party!'
  party-full: '&cThat party is already full!'
  public-party-on: '&aYou are now hosting a public party!'
  public-party-off: '&aYou are no longer hosting a public party!'
  public-party-broadcast: '&e<player>&6 is hosting a public party! Click this message or type &e/party join <player>&6 to join!'
  want-to-invite: '&e<player>&6 has invited you to their party, type &e/party join <player>&6.'
  sent-invite: '&6You have sent an invite!'
  stats-message: '&e<player>&6''s stats'
  #set to 'false' to disable
  started-spectating: '&e<player> &6is now spectating your match!'
  stopped-spectating: '&e<player> &6is no longer spectating your match!'
  #<player>, <hearts>, <hp>
  bow-health-message: '&e<player> &6is now at &e<hearts>&6!'
  invalid-inventory: '&cThe inventory was not found!'
  no-enough-money: '&cYou do not have enough money to do that! You need at least $<needed>.'
  leave-spectator-mode: '&cYou can''t do that command now! &6Type &e/leave &6to leave the spectator mode!'
  party-joined: '&6&e<player>&6 has joined the party!'
  inventory-message: '&6Inventories (click): '
  event-stopped: '&c&lThe event has ended!'
  juggernaut-not-open: '&cThe juggernaut event is not open now!'
  juggernaut-not-started: '&6The juggernaut event hasn''t started yet! Added to the juggernaut queue.'
  juggernaut-eliminated: '&c&lThe juggernaut was eliminated by &a<killer>&c&l!'
  juggernaut-eliminated-logging-out: '&c&lThe juggernaut was eliminated for logging out!'
  juggernaut-join-back: '&aJoin the juggernaut event again with /juggernaut join'
  juggernaut-chance: '&6You have your chance to be the juggernaut!'
  player-is-juggernaut: '&c&lPlayer &a&l<player>&c&l is the juggernaut!'
  party-chat-join: '&aYou are now speaking in the party chat!'
  party-chat-leave: '&aYou are now speaking in the public chat!'
  no-permission: '&cYou do not have permission to do that!'
  does-not-have-party: '&6That player doesn''t have a party anymore!'
  party-wants-duel: '&e<player>&6 has sent a ''<kit>&r&6'' party vs party request.'
  party-wants-duel-with-custom-kit: '&e<player>&6 has sent a custom kit (''<kit>&r&6'') party vs party request.'
  not-online: '&cThe player is not online!'
  has-not-played: '&cThat player has not joined the server!'
  not-enough-players: '&cYour party must have at least 2 players to do that.'
  can-not-do-while-in-match: '&cYou can not do that if you are in a duel or event!'
  can-not-do-while-in-party-other-player: '&cYou can not do that if that player is in a party!'
  can-not-do-while-in-party: '&cYou can not do that if you are in a party!'
  has-not-invited: '&cNo one has invited you.'
  party-has-not-dueled: '&cThat party has not requested your party or the request has expired.'
  showing-stats: '&6Showing &e<player>&6''s stats!'
  not-in-your-party: '&cThat player is not in your party!'
  was-kicked: '&c&e<player>&6 was kicked from the party!'
  left-party: '&c&e<player>&6 has left the party!'
  alread-invited: '&cThat player has already been invited.'
  disband-to-leave: '&cYou are the owner of this party so you must disband the party or promote a member to be able to leave the party.'
  party-was-deleted: '&cThe party has been disbanded!'
  can-not-do-party-commands: '&cYou can not do party commands now!'
  can-not-do-command: '&cYou can not do that command now!'
  no-arenas-found: '&cThere are no free arenas. Please try again soon!'
  request-sent: '&6You have sent a &e<kit> &r&6duel request to &e<target> &6on &e<arena>&r&6!'
  # <player> is the sender, <target> is the player that was dueled (i.e., /duel <target>)
  wants-duel: '&c&e<player>&6 has sent a duel request with the kit <kit>&r&6.'
  bestof-duel-request: '&cThe duel is best of <rounds>!'
  wants-duel-with-custom-kit: '&c&e<player>&6 has sent a custom kit (''<kit>&r&6'')&6 duel request.'
  you-can-not-duel-now: '&cYou can not duel anyone now!'
  waiting-for-duel: '&6Waiting for an opponent in <kit> &r&6queue'
  best-of-round: '&aBest of <total_rounds> rounds. Round: <round>. &aWins: &6<player1>: &e<player1_wins> &6: <player2>: &e<player2_wins>'
  queue-ping-limit: '&cYour are not allowed to join ranked queue because your ping is too high (<ping> ms)'
  no-rankeds-left: '&cYou don''t have any ranked matches left for today.'
  no-unrankeds-left: '&cYou don''t have any unranked matches left for today.'
  no-premium-matches-left: '&cYou don''t have any premium matches left.'
  ranked-kills-required: '&cYou must have at least <required> kills before you can join the ranked queue. You have <kills> kills.'
  has-not-dueled: '&cThat player has not dueled you or the request has expired or the duel can''t be started!'
  left-queue: '&cYou have left your queue!'
  type-name: 'Please type their name here.'
  kit-saved: '&3&lYour kit has been saved!'
  kit-reset: '&3&lYour kit has been reset!'
  party-promoted: '&e&l<player>&6&l was promoted in your party!'
  your-team-won: '&6&lYour team won!'
  your-team-won-disband: '&cYour team won, because the opponent disbanded their party!'
  your-team-did-not-win: '&c&lYour team lost the fight!'
  in-fight: '&cEither your or their team is still in a fight.'
  not-in-fight: '&cThe player must be in a fight.'
  #This message is sent when the fight has 20% of max duration left
  #for example if the limit is 20 minutes this message is sent every minute for (20*0.2 = 4) 4 minutes before the match ends
  fight-duration-limit: '&cThe fight duration is limited to <limit> minutes. <left> minutes left of the match.'
  #sent if the fight exceeds the fight-duration-limit
  fight-duration-force-end: '&cThe fight exceeded fight duration limit and was stopped!'
  ffa-winner: '&e<player>&6 in your party has won the party FFA!'
  your-member-died: '&c&e<player>&6 died! Your team has <alive> players alive.'
  ffa-died: '&c&e<player>&6 died! There are <alive> players alive.'
  opponent-member-died: '&6&e<player>&6 died! The opponent team has <alive> players alive.'
  can-not-teleport: '&cYou can not teleport while in a match!'
  countdown-message: '&6Your match is starting in &e&l<seconds>&r&6 seconds!'
  countdown-go: '&cGO!'
  ffa-arena-reset: '&c&lFFA arenas will reset in <time> seconds!'
  #<elo1> and <elo2> for current elos
  #<old1> and <old2> for old elo
  #<diff1> and <diff2> for elo changes
  elo-fight: '&6Elo changes: <player1> <old1>(+<diff1>) & <player2> <old2>(<diff2>)'
  rank-message: '&c&lYour rank is currently <rank>!'
  you-won: '&6&lYou won the fight!'
  did-not-win: '&c&lYou lost the fight.'
  blocked-command: '&cYou are not allowed to do that command now (leave your current queue, event or match if needed)!'
  lms-joined: '&e<player>&6 joined the LMS event!'
  lms-left: '&e<player>&6 left the LMS event!'
  lms-death-message: '&e<player>&6 has died in the LMS event! <alive> players left!'
  lms-already-started: '&cThe LMS event is not open now!'
  lms-winner: '&e&l<player>&6&l has won the LMS event!'
  lms-has-not-started: '&cThe LMS event is not open yet.'
  koth-winner: '&e&l<team>&6&l has won the KOTH event!'
  koth-join-back: '&aJoin the KOTH event again with /koth join'
  koth-already-started: '&cThe event has already started!'
  koth-has-not-started: '&cThe KOTH event is not open yet.'
  koth-joined: '&e<player>&6 joined the KOTH event!'
  koth-left: '&e<player>&6 left the KOTH event!'
  can-not-duel-that-player: '&cYou can not duel that player now!'
  brackets-next-fight: '&6The next brackets fight is &e<player1> &6VS &e<player2>!'
  brackets-slays: '&e<player1> &6slays &e<player2> &6in the brackets event!'
  brackets-winner: '&e&l<player>&6 has won the brackets event!'
  brackets-joined: '&e<player>&6 has joined the brackets event!'
  brackets-left: '&e<player>&6 has left the brackets event!'
  brackets-already-started: '&cThe event has already started!'
  brackets-not-started: '&cThe brackets event is not open yet!'
  sumo-next-fight: '&6The next sumo fight is &e<player1> &6VS &e<player2>!'
  sumo-slays: '&e<player1> &6slays &e<player2> &6in the sumo event!'
  sumo-winner: '&e&l<player>&6 has won the sumo event!'
  sumo-joined: '&e<player>&6 has joined the sumo event!'
  sumo-left: '&e<player>&6 has left the sumo event!'
  sumo-already-started: '&cThe event has already started!'
  sumo-not-started: '&cThe sumo event is not open yet!'
  language-set: '&6Your language is now &e<language>&6.'
  queue-cooldown: '&cYou must wait &l<time> second(s) &r&cbefore you can join the queue again!'
  elo-queue-cooldown: '&cYou must wait &l<time> second(s) &r&cbefore you can join the elo queue again!'
  duel-requests-off: '&6You will no longer get duel requests.'
  duel-requests-on: '&6You will get duel requests now.'
  has-requests-disabled: '&c&e<player>&6 has disabled duel requests!'
  playback-not-found: '&cSorry, the playback couldn''t be found :('
  kill-cam: '&bClick here to replay the last seconds of the fight.'
  teleporting: '&eTeleporting in <seconds> seconds...'
  teleport-cancelled: '&cThe teleport cancelled because you took damage or moved'
  party-help:
    - '&7----------------&6&lParty Help&7----------------'
    - '&6&l/Party create &r&e- Create an own party'
    - '&6&l/Party invite <player> &r&e- Invite your friend to your party'
    - '&6&l/Party join &r&e- Join a party'
    - '&6&l/Party kick <player> &r&e- Kick your party member from your party'
    - '&6&l/Party leave &r&e- Leave your current party'
    - '&6&l/Party disband &r&e- Disband your party'
    - '&6&l/Party info &r&e- Show info about your party'
    - '&6&l/Party chat &r&e- Chat with other members in your party'
    - '&6&l/Party promote &r&e- Promote a member'
    - '&6&l/Party settings &r&e- Open settings menu'
    - '&6&l/Party fight &r&e- Fight'
    - '&6&l/Party list &r&e- List open parties'
  stats:
    kills: '&6Kills: &e<value>'
    deaths: '&6Deaths: &e<value>'
    elo: '&6<kit> elo: &e<value>'
    party-wins: '&6Party Wins: &e<value>'
    brackets: '&6Brackets Wins: &e<value>'
    lms: '&6LMS Wins: &e<value>'
    global-elo: '&6Global ELO: &e<value>'
  fight-start-message:
    #leave 'false' for no message
    build: '&9You can build with this kit!'
    elo: '&e<you>&6 (&e<your_elo>&6) VS &e<opponent>&6 (&e<opponent_elo>&6)'
    only-bow: '&9You can only use bow in this fight!'
    horse: 'false'
    combo: '&9You can hit a lot faster with this kit!'
    duel: '&91v1 against <opponent>'
    party-ffa: '&9Party FFA against members in your party.'
    party-vs-party: '&9Party VS Party against <opponent>.'
    party-split: '&9Your party was split in two teams.'
    party-bots: '&9Your party VS Bots'
#French
français:
  yes-or-true: '&6Oui'
  no-or-false: '&cNon'
  cooldown-message: '&cTu ne peux pas encore utiliser cela!'
  do-not-spam-things: '&cLe spam est interdit!'
  not-pvp-fight: '&cTu ne peux pas attaquer les autres dans ce combat!'
  party-created: '&aTu as crée un nouveau groupe!'
  can-not-duel-that-party: '&cTu ne peux pas duel ce groupe.'
  playback-can-not-start: '&cL''enregistrement ne peut pas commencer car l''arène n''est pas libre.'
  in-party: '&aTu es déjè dans un groupe!'
  data-not-loaded: '&c&lLes données n''ont pas encore chargé! Veuillez patienter.'
  ranked-range: '&6Recherche de joueurs avec un niveau d''elo compris entre (&e<range1> et <range2>&6).'
  ranked-anyone: '&6Recherche de joueurs de n''importe quel niveau d''elo.'
  invalid-kit: '&cImpossible de visualiser ce kit car celui-ci est invalide!'
  not-own-party: '&cTu n''es le createur d''aucun groupe!'
  stay-away-from-other-players: '&cEn tant que spectateur, veuillez rester a l''écart des autres joueurs!'
  public-party-no-permission: '&cTu n''as pas la permission d''héberger des parties publiques!'
  no-party-limit-bypass: '&cTu n''as pas la permission de changer le nombre de joueurs de ton groupe!'
  party-full: '&cCe groupe est déjà complet!'
  public-party-on: '&aTu heberges maintenant un groupe publique!'
  public-party-off: '&aTu n''héberges plus de groupe publique!'
  public-party-broadcast: '&e<player>&6 heberges un groupe publique! Clique ce message ou entre &e/party join <player>&6 pour rejoindre!'
  want-to-invite: '&e<player>&6 vous a invité dans son groupe, entre &e/party join <player>&6.'
  sent-invite: '&6Tu as envoyé une invitation!'
  stats-message: '&6Stats de &e<player>'
  invalid-inventory: '&cL''inventaire n''a pas été trouvé!'
  no-enough-money: '&cTu n''as pas assez d''argent pour réaliser cela! Tu as besoin d''au moins $<needed>.'
  leave-spectator-mode: '&cTu ne peux pas faire ça! &6Entre &e/leave &6pour quitter le mode spectateur!'
  party-joined: '&6&e<player>&6 a rejoint le groupe!'
  inventory-message: '&6Inventaires (clique): '
  event-stopped: '&c&lL''évènement est terminé!'
  juggernaut-not-open: '&cL''évènement mastodonte est terminée!'
  juggernaut-not-started: '&6L''évènement Mastodonte n''a pas encore commencé! Tu as été ajouté a la file d''attente.'
  juggernaut-eliminated: '&c&lLe mastodonte a été éliminé par &a<killer>&c&l!'
  juggernaut-eliminated-logging-out: '&c&lLe mastodonte a été éliminé par deconnexion!'
  juggernaut-join-back: '&aRejoint l''évènement mastodonte avec /juggernaut join'
  player-is-juggernaut: '&c&lLe joueur &a&l<player>&c&l est le mastodonte!'
  party-chat-join: '&aTu parles désormais dans le chat de groupe!'
  party-chat-leave: '&aTu parles désormais dans le chat public!'
  no-permission: '&cTu n''as pas la permission de réaliser cela!'
  does-not-have-party: '&6Ce joueur n''as plus de groupe !'
  party-wants-duel: '&e<player>&6 a envoyé une requète Groupe vs Groupe en ''<kit>'', entre &e/party duel <player>&6.'
  party-wants-duel-with-custom-kit: '&e<player>&6 a envoyé une requète Groupe vs Groupe avec le kit custom (''<kit>''), entre &e/duel duel <player>&6.'
  not-online: '&cCe joueur n''est pas connecté!'
  has-not-played: '&cCe joueur n''a pas rejoint le serveur!'
  not-enough-players: '&cTon groupe doit contenir au moins 2 joueurs pour cela.'
  can-not-do-while-in-match: '&cTu ne peux pas faire ça si tu es en duel ou en évènement!'
  can-not-do-while-in-party-other-player: '&cTu ne peux pas faire ça si ce joueur est dans un groupe!'
  can-not-do-while-in-party: '&cTu ne peux pas faire ça si tu es dans un groupe!'
  has-not-invited: '&cAucune invitation n''as été reçue.'
  party-has-not-dueled: '&cCe groupe ne vous a pas invité votre groupe ou l''invitation a expiré.'
  showing-stats: '&6Affichage des stats de &e<player>&6!'
  not-in-your-party: '&cCe joueur n''est pas dans votre groupe!'
  was-kicked: '&c&e<player>&6 a été exclu de ce groupe!'
  left-party: '&c&e<player>&6 a quitté ce groupe!'
  alread-invited: '&cCe joueur a déjà été invité.'
  disband-to-leave: '&cTu es le créateur de ce groupe tu dois donc le suppripmer ou promouvoir un membre afin de le quitter.'
  party-was-deleted: '&cCe groupe a été supprimé!'
  can-not-do-party-commands: '&cTu ne peux pas réaliser une commande de groupe maintenant!'
  can-not-do-command: '&cTu ne peux pas réaliser cette commande maintenant!'
  no-arenas-found: '&cAucune arène disponible. Réessaye plus tard!'
  request-sent: '&6Tu as envoyé une invitation de partie!'
  wants-duel: '&c&e<player>&6 a envoyé une requète de duel en ''<kit>'', entre &e/duel accept <player>&6.'
  wants-duel-with-custom-kit: '&c&e<player>&6 a envoyé une requète de duel avec le kit custom (''<kit>''), entre &e/duel accept <player>&6.'
  you-can-not-duel-now: '&cTu ne peux duel personne pour le moment!'
  waiting-for-duel: '&6En attente d''un joueur... Kit: <kit>'
  queue-ping-limit: '&cTu n''es pas autorisé(e) a rejoindre une partie classée car ton ping est trop élevé (<ping> ms)'
  ranked-kills-required: '&cTu dois avoir au moins <required> éliminations avant de pouvoir rejoindre une partie classée. Tu as <kills> éliminations.'
  has-not-dueled: '&cCe joueur ne t''as pas duel ou l''invitation a expiré ou le combat ne peut pas commencer!'
  left-queue: '&cTu as quitté la file d''attente!'
  type-name: 'Veuillez entrer le nom ici'
  kit-saved: '&3&lTon kit a été sauvegardé!'
  kit-reset: '&3&lTon kit a été réinitialisé!'
  party-promoted: '&e&l<player>&6&l a été promu dans votre groupe!'
  your-team-won: '&6&lVotre équipe a gagné!'
  your-team-won-disband: '&cVotre équipe a gagné car l''adversaire a supprimé son groupe!'
  your-team-did-not-win: '&c&lVotre équipe a perdu le combat!'
  in-fight: '&cVotre équipe ou la leur est toujours en combat.'
  not-in-fight: '&cLe joueur doit être en combat.'
  ffa-winner: '&6Le joueur &e<player>&6 de votre groupe a gagné le combat FFA!'
  your-member-died: '&c&e<player>&6 est mort! Votre équipe a <alive> joueurs vivants.'
  ffa-died: '&c&e<player>&6 est mort! Il y a <alive> joueurs vivants.'
  opponent-member-died: '&6&e<player>&6 est mort! Le groupe adverse a <alive> joueurs vivants.'
  can-not-teleport: '&cTu ne peux pas te teleporter durant un combat!'
  countdown-message: '&6Ton combat commence dans &e&l<seconds>&r&6 secondes!'
  countdown-go: '&cC''est partie!'
  ffa-arena-reset: '&c&lL''arène FFA sera réinitialisée dans <time> secondes!'
  #<elo1> and <elo2> for current elos
  #<old1> and <old2> for old elo
  #<diff1> and <diff2> for elo changes
  elo-fight: '&6Elo changes: <player1> <old1>(+<diff1>) & <player2> <old2>(<diff2>)'
  you-won: '&6&lTu as gagné le combat!'
  did-not-win: '&c&lTu as perdu le combat.'
  blocked-command: '&cTu es maintenant autorisé a réaliser cette commande (quitte ta file d''attente, évènement ou duel si besoin)!'
  lms-joined: '&e<player>&6 a rejoint l''évènement DernierJoueurVivant (DJV)!'
  lms-left: '&e<player>&6 a quitté l''évènement DernierJoueurVivant (DJV)!'
  lms-death-message: '&e<player>&6 est mort dans l''evenement DernierJoueurVivant (DJV)! <alive> joueurs restants!'
  lms-already-started: '&cL''évènement DernierJoueurVivant (DJV) est maintenant ouverte!'
  lms-winner: '&e&l<player>&6&l a gagné l''évènement DernierJoueurVivant (DJV)!'
  lms-has-not-started: '&cL''évènement DernierJoueurVivant (DJV) n''est pas encore ouverte.'
  koth-winner: '&e&l<team>&6&l a gagné l''évènement KOTH!'
  koth-join-back: '&aRejoint l''évènement KOTH avec /koth join'
  koth-already-started: '&cL''évènement a déjà commencé!'
  koth-has-not-started: '&cL''évènement KOTH n''est pas encore ouverte.'
  koth-joined: '&e<player>&6 a rejoint l''évènement KOTH!'
  koth-left: '&e<player>&6 a quitté l''évènement KOTH!'
  can-not-duel-that-player: '&cTu ne peux pas duel ce joueur maintenant!'
  brackets-next-fight: '&6Le prochain combat du tournois opposera &e<player1> &6à &e<player2>!'
  brackets-slays: '&e<player1> &6a tué &e<player2> &6pendant l''évènement Tournois!'
  brackets-winner: '&e&l<player>&6 a gagné l''évènement tournois!'
  brackets-joined: '&e<player>&6 a rejoint l''évènement tournois!'
  brackets-left: '&e<player>&6 a quitté l''évènement tournois!'
  brackets-already-started: '&cl''évènement a déjà commencé!'
  brackets-not-started: '&cL''évènement tournois n''est pas encore ouvert!'
  language-set: '&6Your language is now &e<language>&6.'
  queue-cooldown: '&cTu dois attendre &l<time> seconde(s) &r&cavant de rejoindre la file d''attente encore!'
  elo-queue-cooldown: '&cTu dois attendre &l<time> seconde(s) &r&cavant de pouvoir rejoindre la file d''attente elo encore!'
  duel-requests-off: '&6Tu as désactivé les requètes de duels.'
  duel-requests-on: '&6Tu as réactivé les requètes de duels.'
  has-requests-disabled: '&c&e<player>&6 a désactivé ses requètes de duels!'
  playback-not-found: '&cDésolé, l''enregistrement ne peut pas etre retrouvé :('
  teleporting: '&eTéléportation dans <seconds> secondes...'
  teleport-cancelled: '&cLa téléportation a été annulée car tu as bougé ou reçu des degats.'
  party-help:
    - '&6&lCommandes de Groupe:'
    - '&6&l/Party create &r&e- Crée ton groupe'
    - '&6&l/Party invite <player> &r&e- Invite tes amis dans ton groupe'
    - '&6&l/Party join &r&e- Rejoint un groupe'
    - '&6&l/Party kick <player> &r&e- Exclu un membre de ton groupe'
    - '&6&l/Party leave &r&e- Quitte ton groupe actuel'
    - '&6&l/Party disband &r&e- Supprime ton groupe actuel'
    - '&6&l/Party info &r&e- Affiche les informations de ton groupe'
    - '&6&l/Party chat &r&e- Chat avec les autres membres de ton groupe'
    - '&6&l/Party promote &r&e- Promeus un membre de ton groupe'
    - '&6&l/Party settings &r&e- Ouvre les paramètres de ton groupe'
    - '&6&l/Party fight &r&e- Lance un combat de groupe'
  stats:
    kills: '&6Eliminations: &e<value>'
    deaths: '&6Morts: &e<value>'
    elo: '&6<kit> elo: &e<value>'
    party-wins: '&6Victoires Groupe: &e<value>'
    brackets: '&6Victoires Tournois: &e<value>'
    lms: '&6Victoires LMS: &e<value>'
    global-elo: '&6Global ELO: &e<value>'
  fight-start-message:
    #leave 'false' for no message
    build: '&9Tu ne peux pas construire avec ce kit!'
    elo: '&e<you>&6 (&e<your_elo>&6) VS &e<opponent>&6 (&e<opponent_elo>&6)'
    only-bow: '&9Tu peux seulement utiliser ton arc durant ce combat!'
    horse: 'false'
    combo: '&9Tu peux frapper beaucoup plus vite avec ce kit!'
    duel: '&91v1 contre <opponent>'
    party-ffa: '&9Partie FFA contre les membres de ce groupe.'
    party-vs-party: '&9Groupe VS Groupe contre <opponent>.'
    party-split: '&9Votre groupe a été divisé en deux équipes.'
nederlands:
  yes-or-true: '&6Ja'
  no-or-false: '&cNee'
  cooldown-message: '&cJe kan dit nu nog niet gebruiken!'
  do-not-spam-things: '&cNiet spammen alsjeblieft!'
  not-pvp-fight: '&cJe kan anderen niet raken in dit gevecht!'
  party-created: '&aJe hebt een party aangemaakt!'
  can-not-duel-that-party: '&cJe kan deze party niet dueleren.'
  playback-can-not-start: '&cDe playback kan niet worden gestart omdat de arena niet beschikbaar is.'
  in-party: '&aJe zit al in een party!'
  data-not-loaded: '&c&lInformatie nog niet geladen! Wacht een moment alsjeblieft.'
  ranked-range: '&6Op zoek naar spelers in je ELO gebied (&e<range1>-<range2>&6).'
  ranked-anyone: '&6Op zoek naar spelers in elk ELO gebied.'
  invalid-kit: '&cNiet mogelijk om een ongeldige kit te bekijken!'
  not-own-party: '&cJe ben niet de eigenaar van een party!'
  stay-away-from-other-players: '&cBlijf alsjeblieft uit de buurt van andere spelers wanneer je toeschouwer bent!'
  public-party-no-permission: '&cJe hebt geen rechten om publieke parties te starten!'
  no-party-limit-bypass: '&cJe hebt geen rechten om de spelers limiet van jou party te veranderen!'
  party-full: '&cDe party is al vol!'
  public-party-on: '&aJe bent nu host van een publieke party!'
  public-party-off: '&aJe bent niet langer host van een publieke party!'
  public-party-broadcast: '&e<player>&6 is nu host van een publieke party! Klik op dit bericht of type &e/party join <player>&6 om de party toe te treden!'
  want-to-invite: '&e<player>&6 Heeft je uitgenodigd om zijn party toe te treden, type &e/party join <player>&6.'
  sent-invite: '&6Je hebt een uitnodiging verstuurd!'
  stats-message: '&6&e<player>&6''s statistieken'
  invalid-inventory: '&cDe inventaris is niet gevonden!'
  no-enough-money: '&cJe hebt niet genoeg geld om dat te doen! Je hebt op zijn minst $<needed> nodig.'
  leave-spectator-mode: '&cJe kan nu dat command niet gebruiken! &6Type &e/leave &6om uit toeschouwer modus te komen!'
  party-joined: '&6&e<player>&6 is de party toegetreden!'
  inventory-message: '&6Inventarissen (Klik): '
  event-stopped: '&c&lHet evenement is voorbij!'
  juggernaut-not-open: '&cHet Juggernaut evenement is op dit moment niet open!'
  juggernaut-not-started: '&6Het Juggernaut evenement is nog niet gestart! Toegevoegd tot de Juggenaut wachtrij.'
  juggernaut-eliminated: '&c&lDe Juggernaut was geëlimineerd door &a<killer>&c&l!'
  juggernaut-eliminated-logging-out: '&c&lDe Juggernaut is geëlimineerd omdat hij uitlogde!'
  juggernaut-join-back: '&aDoe mee aan het Juggernaut evenement door /juggernaut join te typen'
  player-is-juggernaut: '&c&lSpeler &a&l<player>&c&l is de Juggernaut!'
  party-chat-join: '&aJe spreekt nu in de party chat!'
  party-chat-leave: '&aJe spreekt nu in de publieke chat!'
  no-permission: '&cJe hebt geen rechten om dat te doen!'
  does-not-have-party: '&6Die speler heeft geen party meer!'
  party-wants-duel: '&e<player>&6 Heeft een ''<kit>'' party vs party verzoek verstuurd, type &e/party duel <player>&6.'
  party-wants-duel-with-custom-kit: '&e<player>&6 Heeft een custom kit (''<kit>'') party vs party verzoek gestuurd type &e/party duel <player>&6.'
  not-online: '&cDe speler is niet online!'
  has-not-played: '&cDie speler heeft de server nog nooit gejoined!'
  not-enough-players: '&cJouw party heeft op zijn minst 2 spelers nodig om dat te doen.'
  can-not-do-while-in-match: '&cJe kan dat niet doen terwijl je in een duel of evenement zit!'
  can-not-do-while-in-party-other-player: '&cJe kan dat niet doen wanneer die speler in een party zit!'
  can-not-do-while-in-party: '&cJe kan dat niet doen terwijl je in een party zit!'
  has-not-invited: '&cNiemand heeft jou een uitnodiging verstuurd.'
  party-has-not-dueled: '&cDie party heeft jou niks aangevraagd of de aanvraag is verlopen.'
  showing-stats: '&6 &e<player>&6''s zijn statistieken aan het tonen!'
  not-in-your-party: '&cDie speler zit niet in jouw party!'
  was-kicked: '&c&e<player>&6 is uit de party geschopt!'
  left-party: '&c&e<player>&6 heeft de party verlaten!'
  alread-invited: '&cDie speler is al uitgenodigd.'
  disband-to-leave: '&cJe bent de eigenaar van deze party, je moet dus de party verwijderen of een andere speler promoten tot eigenaar om de party te verlaten.'
  party-was-deleted: '&cDe party is verwijderd!'
  can-not-do-party-commands: '&cJe kan geen party commands op dit moment doen!'
  can-not-do-command: '&cJe kan die command nu niet doen!'
  no-arenas-found: '&cEr zijn geen vrije arenas. Probeer later opnieuw!'
  request-sent: '&6Je hebt een wedstrijd uitnodiging gestuurd!'
  wants-duel: '&c&e<player>&6 Heeft een ''<kit>'' duel verzoek gestuurd, type &e/duel accept <player>&6.'
  wants-duel-with-custom-kit: '&c&e<player>&6 Heeft een custom kit (''<kit>'') duel verzoek gestuurd, type &e/duel accept <player>&6.'
  you-can-not-duel-now: '&cJe kan op dit moment niemand dueleren!'
  waiting-for-duel: '&6Aan het wachten op een tegenstander... Wachtrij: <kit>'
  queue-ping-limit: '&cJe bent niet toegestaan om de ranked wachtrij toe te treden omdat je ping te hoog is (<ping> ms)'
  no-rankeds-left: '&cJe hebt geen ranked wedstrijden over voor vandaag.'
  no-unrankeds-left: '&cJe hebt geen unranked wedstrijden over voor vandaag.'
  no-premium-matches-left: '&cJe hebt geen premium wedstrijden over.'
  ranked-kills-required: '&cJe hebt op zijn minst <required> kills nodig voordat je een ranked wedstrijd kan beginnen. Jij hebt <kills> kills.'
  has-not-dueled: '&cDie speler heeft jou niet gedueleerd of de aanvraag is verlopen of het duel kan niet worden gestart!'
  left-queue: '&cJe hebt de wachtrij verlaten!'
  type-name: 'Alsjeblieft type zijn naam hier.'
  kit-saved: '&3&lJe kits zijn opgeslagen!'
  kit-reset: '&3&lJe kit is gereset!'
  party-promoted: '&e&l<player>&6&l is gepromoveerd in je party!'
  your-team-won: '&6&lJe hebt gewonnen!'
  your-team-won-disband: '&cJe team heeft gewonnen, omdat de tegenstander de party heeft verwijderd!'
  your-team-did-not-win: '&c&lJe team heeft verloren!'
  in-fight: '&cJouw of hun team is nog steeds in een gevecht.'
  not-in-fight: '&cDe speler is in een gevecht.'
  ffa-winner: '&e<player>&6 in jouw party heeft de FFA gewonnen!'
  your-member-died: '&c&e<player>&6 is dood! Jouw team heeft <alive> levende spelers over.'
  ffa-died: '&c&e<player>&6 is dood! Er zijn <alive> levende spelers over.'
  opponent-member-died: '&6&e<player>&6 is dood! Het andere team heeft <alive> levende spelers over.'
  can-not-teleport: '&cJe kan niet teleporteren wanneer je in een gevecht zit!'
  countdown-message: '&6je wedstrijd start in &e&l<seconds>&r&6 seconden!'
  countdown-go: '&6&lVECHT!'
  ffa-arena-reset: '&c&lFFA arenas worden gereset in <time> seconden!'
  #<elo1> en <elo2> voor elo nu
  #<old1> en <old2> voor oude elo
  #<diff1> en <diff2> voor elo verschil
  elo-fight: '&6Elo verschil: <player1> <old1>(+<diff1>) & <player2> <old2>(<diff2>)'
  you-won: '&6&lJe hebt het gevecht gewonnen!'
  did-not-win: '&c&lJe hebt het gevecht verloren.'
  blocked-command: '&cJe bent nu in staat om die command te gebruiken (Verlaat je wachtrij, evenement of gevecht wanneer nodig)!'
  lms-joined: '&e<player>&6 is het LMS evenement toegetreden!'
  lms-left: '&e<player>&6 heeft het LMS evenement verlaten!'
  lms-death-message: '&e<player>&6 is dood gegaan in het LMS evenement! <alive> spelers over!'
  lms-already-started: '&cHet evenement is al begonnen!'
  lms-winner: '&e&l<player>&6&l heeft het LMS evenement gewonnen!'
  lms-has-not-started: '&cHet LMS evenement is nog niet open.'
  koth-winner: '&e&l<team>&6&l heeft het KOTH evenement gewonnen!'
  koth-join-back: '&aKom terug naar het KOTH evenement door /koth join te typen'
  koth-already-started: '&cHet evenement is al begonnen!'
  koth-has-not-started: '&cHet KOTH evenement is nog niet open.'
  koth-joined: '&e<player>&6 is het KOTH evenement toegetreden!'
  koth-left: '&e<player>&6 heeft het KOTH evenement verlaten!'
  can-not-duel-that-player: '&cJe kan die speler nu niet dueleren!'
  brackets-next-fight: '&6Het volgende gevecht is: &e<player1> &6VS &e<player2>!'
  brackets-slays: '&e<player1> &6verslaat &e<player2> &6in het brackets evenement!'
  brackets-winner: '&e&l<player>&6 Heeft het brackets evenement gewonnen!'
  brackets-joined: '&e<player>&6 is het brackets evenement toegetreden!'
  brackets-left: '&e<player>&6 heeft het brackets evenement verlaten!'
  brackets-already-started: '&cHet evenement is al begonnen!'
  brackets-not-started: '&cHet brackets evenement is nog niet open!'
  sumo-next-fight: '&6Het volgende gevecht is: &e<player1> &6VS &e<player2>!'
  sumo-slays: '&e<player1> &6verslaat &e<player2> &6in het sumo evenement!'
  sumo-winner: '&e&l<player>&6 Heeft het sumo evenement gewonnen!'
  sumo-joined: '&e<player>&6 is het sumo evenement toegetreden!'
  sumo-left: '&e<player>&6 heeft het sumo evenement verlaten!'
  sumo-already-started: '&cHet evenement is al begonnen!'
  sumo-not-started: '&cHet sumo evenement is nog niet open!'
  language-set: '&6Je taal is nu: &e<language>&6.'
  queue-cooldown: '&cJe moet &l<time> seconde(n) &r&cwachten voordat je een nieuwe wachtrij kan toetreden!'
  elo-queue-cooldown: '&cJe moet &l<time> seconde(n) &r&cwachten voordat je een nieuwe elo wachtrij kan toetreden!'
  duel-requests-off: '&6Je krijgt voortaan geen duel verzoeken meer.'
  duel-requests-on: '&6Je krijgt voortaan weer duel verzoeken.'
  has-requests-disabled: '&c&e<player>&6 heeft duel verzoeken uit staan!'
  playback-not-found: '&cSorry, de playback kon niet worden gevonden :('
  teleporting: '&eTeleportatie in <seconds> seconden...'
  teleport-cancelled: '&cDe teleportatie is gestopt omdat je geraakt werd of je bewoog.'
  party-help:
    - '&6&lParty Help:'
    - '&6&l/Party create &r&e- Creër je eigen party'
    - '&6&l/Party invite <player> &r&e- Nodig een vriend uit tot je party'
    - '&6&l/Party join &r&e- Treed een party toe'
    - '&6&l/Party kick <player> &r&e- Schop iemand uit je party'
    - '&6&l/Party leave &r&e- Verlaat je party'
    - '&6&l/Party disband &r&e- Verwijder je party'
    - '&6&l/Party info &r&e- Laat informatie zien over je party'
    - '&6&l/Party chat &r&e- Chat met andere van je party'
    - '&6&l/Party promote &r&e- Promoot een speler in je party'
    - '&6&l/Party settings &r&e- Open het settings menu'
    - '&6&l/Party fight &r&e- Vecht'
  stats:
    kills: '&6Kills: &e<value>'
    deaths: '&6Deaths: &e<value>'
    elo: '&6<kit> elo: &e<value>'
    party-wins: '&6Party Wins: &e<value>'
    brackets: '&6Brackets Wins: &e<value>'
    lms: '&6LMS Wins: &e<value>'
    global-elo: '&6Globale ELO: &e<value>'
  fight-start-message:
    #leave 'false' for no message
    build: '&6&lCampen of skybasen wordt gestrafd!'
    elo: '&e<you>&6 (&e<your_elo>&6) VS &e<opponent>&6 (&e<opponent_elo>&6)'
    only-bow: 'false'
    horse: 'false'
    combo: 'false'
    duel: '&6&lOpponent: <opponent>'
    party-ffa: '&6&lParty FFA tegen de spelers van je party.'
    party-vs-party: '&6&lParty VS Party tegen <opponent>.'
    party-split: '&6&lJe party is door tweeën gedeeld.'
#Thanks to JuliCarles
spanish:
  yes-or-true: '&6Si'
  no-or-false: '&cNo'
  cooldown-message: '&cNo puedes utilizar esto todavía! Debes esperar <time>.'
  do-not-spam-things: '&c¡No tan rápido! Espera unos segundos para hacer esto nuevamente.'
  not-pvp-fight: '&c¡No puedes golpear a otros en esta partida!'
  party-created: '&a¡Has creado una nueva party!'
  can-not-duel-that-party: '&cNo puedes realizar duelos con esta party.'
  playback-can-not-start: '&cLa repeticion no pudo ser iniciada porque la arena no está disponible momentáneamente.'
  in-party: '&a¡Ya estás en una party!'
  data-not-loaded: '&c&lLa información aún no ha sido cargada. Espera unos segundos.'
  ranked-range: '&6Buscando jugadores en tu rango de elo (&e<range1>-<range2>&6).'
  ranked-anyone: '&6Buscando jugadores en cualquier rango de elo.'
  invalid-kit: '&c¡No puedes previsualizar un kit inválido!'
  not-own-party: '&c¡Actualmente no eres dueño de ninguna party!'
  stay-away-from-other-players: '&c¡Por favor, aléjate de los jugadores mientras juegan!'
  public-party-no-permission: '&c¡No tienes permisos para ser anfitrion de una party pública!'
  no-party-limit-bypass: '&cNo tienes permisos para cambiar el limite de usuarios en tu party!'
  party-full: '&c¡Esa party ya está llena!'
  public-party-on: '&a¡Eres ahora anfitrion de una party pública!'
  public-party-off: '&a¡Ya no eres anfitrion de una party pública!'
  public-party-broadcast: '&6¡&e<player> &6ha creado una party &6pública! &6Click aqui o &e/party join <player>&6 para &6unirte!'
  want-to-invite: '&e<player>&6 te ha invitado a su party, escribe &e/party join <player>&6 para unirte.'
  sent-invite: '&6Has enviado una invitación!'
  stats-message: '&6Estadísticas de &e<player>'
  #set to 'false' to disable
  started-spectating: '&6¡&e<player> &6está viendo tu partida!'
  stopped-spectating: '&6¡&e<player> &6ya no está viendo tu partida!'
  #<player>, <hearts>, <hp>
  bow-health-message: '&e<player> &6está ahora a &e<hearts>!'
  invalid-inventory: '&c¡El inventario no ha sido encontrado!'
  no-enough-money: '&c¡No tienes el suficiente dinero para comprar esto! Necesitas al menos <needed> unicoins.'
  leave-spectator-mode: '&c¡No puedes ejecutar ese comando! &6¡Escribe &e/leave &6para salir del modo espectador!'
  party-joined: '&6¡&e<player>&6 se ha unido a tu party!'
  inventory-message: '&6Inventarios (click): '
  event-stopped: '&c&l¡El evento ha finalizado!'
  juggernaut-not-open: '&c¡El evento juggernaut está ahora abierto!'
  juggernaut-not-started: '&6¡El evento juggernaut aun no ha empezado! Has sido añadido a la lista de espera.'
  juggernaut-eliminated: '&c&l¡El juggernaut ha sido eliminado por &a<killer>&c&l!'
  juggernaut-eliminated-logging-out: '&c&l¡El juggernaut fue eliminado por desconectarse!'
  juggernaut-join-back: '&aÚnete al evento juggernaut nuevamente con /juggernaut join.'
  player-is-juggernaut: '&c&l¡El jugador &a&l<player>&c&l es el juggernaut!'
  party-chat-join: '&a¡Estás ahora hablando en el chat party!'
  party-chat-leave: '&a¡Estás ahora hablando en el chat publico!'
  no-permission: '&c¡No tienes permisos para realizar esto!'
  does-not-have-party: '&6¡Ese jugador ya no tiene una party!'
  party-wants-duel: '&e<player>&6 ha enviado una party vs party con el kit ''<kit>''. Escribe &e/party duel <player>&6 para aceptar.'
  party-wants-duel-with-custom-kit: '&e<player>&6 ha enviado una party vs party con el kit custom (''<kit>''). Escribe &e/duel duel <player>&6 para aceptar.'
  not-online: '&c¡Ese jugador no está conectado!'
  has-not-played: '&c¡Ese jugador no se ha unido al servidor!'
  not-enough-players: '&cTu party debe tener al menos 2 jugadores para comenzar la partida.'
  can-not-do-while-in-match: '&c¡No puedes realizar eso si estás en duelo o evento!'
  can-not-do-while-in-party-other-player: '&c¡No puedes realizar eso si el jugador está en una party!'
  can-not-do-while-in-party: '&c¡No puedes realizar esto si estas en una party!'
  has-not-invited: '&cNadie te ha invitado.'
  party-has-not-dueled: '&cEsa party no ha solicitado estar contigo o la solicitud ya ha expirado.'
  showing-stats: '&6Mostrando las estadísticas de &e<player>&6!'
  not-in-your-party: '&c¡Ese jugador no es de tu party!'
  was-kicked: '&c¡&e<player>&6 ha sido expulsado de la party!'
  left-party: '&c¡&e<player>&6 ha dejado la party!'
  alread-invited: '&cEse jugador ya ha sido invitado antes.'
  disband-to-leave: '&cTu eres el dueño de la party, si quieres salir debes romper la party o promover de rango a otro jugador.'
  party-was-deleted: '&cEl dueño de la party ha decidido romperla.'
  can-not-do-party-commands: '&c¡No puedes utilizar comandos party en este momento!'
  can-not-do-command: '&c¡No puedes utilizar este comando ahora!'
  no-arenas-found: '&c¡No hay arenas disponibles momentáneamente. Por favor, intenta nuevamente mas tarde!'
  request-sent: '&6¡Has enviado una solicitud de party!'
  wants-duel: '&c&e<player>&6 te ha enviado un duelo con el siguiente kit: &a&l<kit>&6. &6Escribe &e/duel accept <player>&6 para aceptar.'
  wants-duel-with-custom-kit: '&c&e<player>&6 te ha enviado un duelo con el siguiente kit custom: &a&l<kit>&6. &6Escribe &e/duel accept <player>&6 para aceptar.'
  you-can-not-duel-now: '&c¡No puedes realizar duelos por el momento!'
  waiting-for-duel: '&6Esperando un oponente en: <kit>'
  best-of-round: '&aMejor de <total_rounds> rondas. Ronda: <round>. &aVictorias: &6<player1>: &e<player1_wins> &6: <player2>: &e<player2_wins>'
  queue-ping-limit: '&cNo tienes permitido unirte a una partida ranked debido a que tu ping es muy alto. (<ping> ms)'
  no-rankeds-left: '&cTus rankeds se han acabado por el día de hoy.'
  no-unrankeds-left: '&cTus unrankeds por el dia de hoy.'
  no-premium-matches-left: '&cNo tienes ninguna partida premium restante.'
  ranked-kills-required: '&cDebes tener al menos <required> asesinatos antes de poder unirte a la lista de espera de rankeds. Tienes <kills> asesinatos.'
  has-not-dueled: '&cEse jugador no te ha enviado solicitud de duelo o la solicitud ha expirado!'
  left-queue: '&cHas dejado la lista de espera!'
  type-name: 'Por favor escribe su nombre aqui.'
  kit-saved: '&3&lTu kit ha sido guardado!'
  kit-reset: '&3&lTu kit ha sido reiniciado!'
  party-promoted: '&6&l¡&e&l<player>&6&l ha sido promovido en tu party!'
  your-team-won: '&6&l¡Tu equipo ganó!'
  your-team-won-disband: '&c&l¡Tu equipo ganó porque el oponente ha roto su party!'
  your-team-did-not-win: '&c&l¡Tu equipo ha perdido la partida!'
  in-fight: '&cTanto tu como tu equipo ya están en partida.'
  not-in-fight: '&cEl jugador debe estar en partida.'
  ffa-winner: '&6¡&e<player>&6 ha ganado la FFA de party!'
  your-member-died: '&6¡&e<player>&6 ha muerto! Tu equipo tiene &e<alive> &6jugadores restantes.'
  ffa-died: '&6¡&e<player>&6 ha muerto! Tu equipo tiene &e<alive> &6jugadores restantes.'
  opponent-member-died: '&6¡&e<player>&6 ha muerto! El oponente tiene &e<alive> &6jugadores restantes.'
  can-not-teleport: '&c¡No te puedes transportar mientras estas en una partida!'
  countdown-message: '&6¡Tu partida comenzara en &e&l<seconds>&r&6 segundos!'
  countdown-go: '&c¡VAMOS!'
  ffa-arena-reset: '&c&l¡Las arenas FFA se reiniciarán en <time> segundos!'
  #<elo1> and <elo2> for current elos
  #<old1> and <old2> for old elo
  #<diff1> and <diff2> for elo changes
  elo-fight: '&6Cambios en el Elo: <player1> <old1>(+<diff1>) & <player2> <old2>(<diff2>)'
  you-won: '&6&l¡Has ganado la partida!'
  did-not-win: '&c&lHas perdido la partida.'
  blocked-command: '&c¡No tienes permitido utilizar este comando momentáneamente!'
  lms-joined: '&6¡&e<player>&6 se ha unido al evento LMS!'
  lms-left: '&6¡&e<player>&6 se ha unido al evento LMS!'
  lms-death-message: '&6¡&e<player>&6 ha muerto en el evento LMS! <alive> jugadores restantes!'
  lms-already-started: '&6¡&cEl evento LMS esta ahora abierto!'
  lms-winner: '&6&l¡&e&l<player>&6&l ha ganado el evento LMS!'
  lms-has-not-started: '&cEl evento LMS aún no ha empezado.'
  koth-winner: '&6&l¡&e&l<team>&6&l ha ganado el evento KOTH!'
  koth-join-back: '&aÚnete nuevamente al evento KOTH utilizando /koth join.'
  koth-already-started: '&c¡El evento ya ha comenzado!'
  koth-has-not-started: '&c¡El evento KOTH aún no ha empezado.'
  koth-joined: '&6¡&e<player>&6 se ha unido al evento KOTH!'
  koth-left: '&6¡&e<player>&6 ha abandonado el evento KOTH!'
  can-not-duel-that-player: '&c¡No puedes luchar contra ese usuario por ahora!'
  brackets-next-fight: '&6¡La siguiente lucha sera entre &e<player1> &6VS &e<player2>!'
  brackets-slays: '&6¡&e<player1> &6ha asesinado a &e<player2> &6en el evento brackets!'
  brackets-winner: '&6¡&e&l<player>&6 ha ganado el evento brackets!'
  brackets-joined: '&6¡&e<player>&6 se ha unido al evento brackets!'
  brackets-left: '&6¡&e<player>&6 ha abandonado el evento brackets!'
  brackets-already-started: '&c¡El evento ya ha comenzado!'
  brackets-not-started: '&c¡El evento brackets aún no ha comenzado!'
  sumo-next-fight: '&6¡La siguiente lucha de sumo es entre &e<player1> &6VS &e<player2>!'
  sumo-slays: '&6¡&e<player1> &6ha asesinado a &e<player2> &6en el evento sumo!'
  sumo-winner: '&6¡&e&l<player>&6 ha ganado el evento sumo!'
  sumo-joined: '&6¡&e<player>&6 se ha unido al evento sumo!'
  sumo-left: '&6¡&e<player>&6 ha abandonado el evento sumo!'
  sumo-already-started: '&c¡El evento ya ha comenzado!'
  sumo-not-started: '&c¡El evento sumo aún no ha comenzado!'
  language-set: '&6¡Tu lenguaje es ahora: &e<language>&6.'
  queue-cooldown: '&c¡Debes esperar &l<time> segundo(s) &r&cantes de poder entrar en la lista de espera de nuevo!'
  elo-queue-cooldown: '&c¡Debes esperar &l<time> segundo(s) &r&cantes de poder entrar en la lista de espera de elo de nuevo!'
  duel-requests-off: '&6Has desactivado los duelos. Ya no los recibirás.'
  duel-requests-on: '&6Has activado los duelos.'
  has-requests-disabled: '&c&e<player>&6 ha desactivado los duelos!'
  playback-not-found: '&cLo lamentamos, la repeticion no pudo ser encontrada'
  kill-cam: '&bClick aqui para repetir los ultimos segundos de la partida.'
  teleporting: '&eTransportando en <seconds> segundos...'
  teleport-cancelled: '&cLa teletransportacion ha sido cancelada porque has recibido daño o te has movido.'
  party-help:
    - '&6&lAyuda de Party:'
    - '&6&l/Party create &r&e- Crea tu propia party'
    - '&6&l/Party invite <player> &r&e- Invita a jugadores a tu party'
    - '&6&l/Party join &r&e- Unete a una pary'
    - '&6&l/Party kick <player> &r&e- Expulsa a un miembro de tu party'
    - '&6&l/Party leave &r&e- Deja tu actual party'
    - '&6&l/Party disband &r&e- Rompe tu actual party'
    - '&6&l/Party info &r&e- Muestra informacion acerca de tu party'
    - '&6&l/Party chat &r&e- Activa el chat de party'
    - '&6&l/Party promote &r&e- Promueve a un jugador a otro rango'
    - '&6&l/Party settings &r&e- Abre el menu de configuraciones'
    - '&6&l/Party fight &r&e- Lucha'
  stats:
    kills: '&6Asesinatos: &e<value>'
    deaths: '&6Muertes: &e<value>'
    elo: '&6<kit> elo: &e<value>'
    party-wins: '&6Victorias de Party: &e<value>'
    brackets: '&6Victorias de Brackets: &e<value>'
    lms: '&6Victorias de LMS: &e<value>'
    global-elo: '&6ELO Global: &e<value>'
  fight-start-message:
    #leave 'false' for no message
    build: 'false'
    elo: '&e<you>&6 (&e<your_elo>&6) VS &e<opponent>&6 (&e<opponent_elo>&6)'
    only-bow: '&9Solo puedes utilizar arco en esta partida!'
    horse: 'false'
    combo: '&9Puedes golpear mucho mas rapido con este kit!'
    duel: '&91v1 contra <opponent>'
    party-ffa: '&9Party FFA contra los miembros de tu party.'
    party-vs-party: '&9Party VS Party contra <opponent>.'
    party-split: '&9Tu party ha sido dividida en 2 equipos.'
  rank-message: '&c&lTu rango es ahora: <rank>!'
  bestof-duel-request: '&cEl número de rondas elegidas es: mejor de <rounds> rondas!'
#Finnish
suomi:
  yes-or-true: '&6Joo'
  no-or-false: '&cEi'
  cooldown-message: '&6Et voi käyttää tuota vielä uudestaan! Sinun täytyy odottaa <time>'
  do-not-spam-things: '&cÄlä späm-klikkaa asioita!'
  not-pvp-fight: '&cEt voi lyödä muita tässä taistelussa!'
  party-created: '&aTeit partyn!'
  can-not-duel-that-party: '&cEt voi haastaa tuota partya.'
  playback-can-not-start: '&cToistoa ei voida aloittaa, koska areena ei ole vapaa.'
  in-party: '&cOlet jo partyssa!'
  invalid-kit: '&cEi pystytä esikatselemaan pätemätöntä kittiä.'
  not-own-party: '&cEt ole minkään partyn omistaja!'
  stay-away-from-other-players: '&cPysy kauempana muista pelaajista katsojana!'
  public-party-no-permission: '&cSinulla ei ole lupaa hostata julkista partya!'
  no-party-limit-bypass: '&cEt voi vaihtaa partysi pelaajamäärärajaa!'
  party-full: '&cTuo party on jo täynnä!'
  public-party-on: '&aHostaat julkista partya nyt!'
  public-party-off: '&aEt enää hostaa julkista partya!'
  public-party-broadcast: '&e<player>&6 hostaa julkista partya! Klikkaa tätä viestiä tai kirjoita &e/party join <player>&6 liittyäksesi!'
  want-to-invite: '&e<player>&6 haluaa lisätä sinut hänen partyynsä, kirjoita &e/party join <player>&6.'
  data-not-loaded: '&cTiedot eivät ole latautuneet vielä! Odota hetki.'
  ranked-range: '&6Etsitään pelaajia elo-alueella (&e<range1>-<range2>&6).'
  ranked-anyone: '&6Etsitään pelaajia kaikilla elo-alueilla.'
  sent-invite: '&6Lähetit kutsun!'
  no-enough-money: '&cSinulla ei ole riittävästi rahaa tehdäksesi tuon! Tarvitset vähintään $<needed>.'
  stats-message: '&5Pelaajan &e<player>&6 tilastot'
  started-spectating: '&e<player> &6katsoo peliäsi!'
  stopped-spectating: '&e<player> &6ei katso peliäsi enää!'
  invalid-inventory: '&cTavaraluetteloa ei löytynyt!'
  leave-spectator-mode: '&cEt voi tehdä tuota komentoa nyt. Poistu katsoja tilasta kirjoittamalla &e/leave&c!'
  party-joined: '&6&e<player>&6 liittyi partyyn!'
  inventory-message: '&6Tavaraluettelot (klikkaa):'
  event-stopped: '&c&lEvent loppui!'
  juggernaut-already-started: '&cJuggernaut event on jo alkanut!'
  juggernaut-not-started: '&6Juggernaut event ei ole alkanut vielä! Sinut lisättiin juggernaut jonoon!'
  juggernaut-eliminated: '&c&lJuggernautin tappoi &a<killer>&c&l!'
  juggernaut-eliminated-logging-out: '&c&lJuggernaut eliminoitiin uloskirjautumisen takia!'
  juggernaut-join-back: '&aLiity takaisin juggernaut eventtiin komennolla /juggernaut join'
  player-is-juggernaut: '&6&lPelaaja &e&l<player>&6&l on juggernaut!'
  party-chat-join: '&aOlet nyt party chatissa!'
  party-chat-leave: '&aOlet nyt julkisessa chatissa!'
  no-permission: '&cSinulla ei ole lupaa tehdä tuota!'
  does-not-have-party: '&6Tuolla pelaajalla ei ole enää partya!'
  party-wants-duel: '&e<player>&6 haastoi partysi kitin ''<kit>'' kanssa, kirjoita &e/party duel <player>&6.'
  party-wants-duel-with-custom-kit: '&e<player>&6 haastoi partysi custom kitin ''<kit>&6'' kanssa, kirjoita &e/party duel <player>&6.'
  not-online: '&cPelaajaa ei löytynyt!'
  has-not-played: '&cTuo pelaaja ei ole pelannut serverillä!'
  not-enough-players: '&cPartyssasi ei ole 2 tai useampaa pelaajaa.'
  can-not-do-while-in-match: '&cEt voi tehdä tuota jos sinä olet taistelussa tai eventissä!'
  can-not-do-while-in-party-other-player: '&cEt voi tehdä tuota jos hän on partyssa!'
  can-not-do-while-in-party: '&cEt voi tehdä tuota jos sinä olet partyssa!'
  has-not-invited: '&cKukaan ei ole kutsunut sinua.'
  showing-stats: '&6Näytetään pelaajan &e<player>&6 tilastot!'
  party-has-not-dueled: '&cTuo party ei ole lähettänyt pyyntöä partyllesi tai aika umpeutui.'
  not-in-your-party: '&cTuo pelaaja ei ole sinun partyssasi!'
  was-kicked: '&c&e<player>&6 kickattiin partystasi!'
  left-party: '&c&e<player>&6 lähti partystasi!'
  alread-invited: '&cTuo pelaaja on jo kutsuttu.'
  disband-to-leave: '&cOlet tämän partyn omistaja, joten et voi lähteä partysta, mutta voit tuhota partysi tai ylentämällä partysi jäsenen.'
  party-was-deleted: '&cPartysi poistettin!'
  can-not-do-party-commands: '&cEt voi tehdä party komentoja nyt!'
  can-not-do-command: '&cEt voi tehdä tuota komentoa nyt!'
  no-arenas-found: '&cVapaita areenoita ei löytynyt. Kokeile uudelleen hetken kuluttua!'
  request-sent: '&6Lähetit taistelupyynnön!'
  wants-duel: '&e<player>&6 haastoi sinut kitin ''<kit>'' kanssa, kirjoita &e/duel accept <player>&6.'
  wants-duel-with-custom-kit: '&e<player>&6 haastoi sinut custom kitin (''<kit>&6'') kanssa, kirjoita &e/duel accept <player>&6.'
  you-can-not-duel-now: '&cEt voi haastaa ketään nyt!'
  waiting-for-duel: '&6Odotetaan vastustajaa... Kitti: <kit>'
  best-of-round: '&aParas <total_rounds> erästä. Erä: <round>. &aVoitot: &6<player1>: &e<player1_wins>&6 : <player2>: &e<player2_wins>'
  queue-ping-limit: '&cEt voi liittyä ranked jonoon, koska pingisi on liian korkea (<ping> ms)'
  no-rankeds-left: '&cSinulla ei ole yhtään ranked otteluja jäljellä tälle päivälle.'
  no-unrankeds-left: '&cSinulla ei ole yhtään unranked otteluja jäljellä tälle päivälle.'
  no-premium-matches-left: '&cSinulla ei ole premium otteluita jäljellä.'
  ranked-kills-required: '&cSinulla täytyy olla vähintään <required> tappoa ennen kuin voit liittyä ranked jonoon. Sinulla on <kills> tappoa.'
  has-not-dueled: '&cTuo pelaaja ei ole haastanut sinua tai aika umpeutui tai hän on nyt taistelussa!'
  left-queue: '&cPoistuit nykyisestä jonostasi!'
  type-name: 'Kirjoita nimi tänne'
  kit-saved: '&3Kittisi tallennettiin!'
  kit-reset: '&3Kittisi resetoitiin!'
  party-promoted: '&c&e<player>&6 ylennettiin partyssasi!'
  your-team-won: '&cGG! &5Tiimisi voitti!'
  your-team-won-disband: '&5Tiimisi voitti, koska vastustajasi tuhosi heidän partynsa!'
  your-team-did-not-win: '&5Tiimisi hävisi!'
  in-fight: '&cTiimisi tai vihollistiimisi on edelleen taistelussa.'
  not-in-fight: '&cPelaajan pitää olla pelissä.'
  ffa-winner: '&c&e<player>&6 partyssasi voitti party FFA eventin!'
  your-member-died: '&6Pelaaja &e<player>&6 tiimissäsi kuoli! Tiimilläsi on <alive> pelaajaa jäljellä.'
  ffa-died: '&c&e<player>&6 kuoli! <alive> pelaajaa jäljellä.'
  opponent-member-died: '&6Pelaaja &e<player>&6 vihollistiimissä kuoli! Vihollistiimissä on <alive> pelaajaa jäljellä.'
  can-not-teleport: '&cEt voi teleportata taistelussa!'
  countdown-message: '&6Taistelusi alkaa &l&e<seconds>&r&6 sekunnin kuluttua!'
  countdown-go: '&cGO!'
  ffa-arena-reset: '&c&lFFA areenat resetöidään <time> sekunnissa!'
  you-won: '&6Voitit 1v1 taistelun!'
  elo-fight: '&6Elo muutokset: <player1> <old1>(+<diff1>) & <player2> <old2>(<diff2>)'
  did-not-win: '&6Hävisit taistelun.'
  blocked-command: '&cEt voi tehdä tuota komentoa nyt (poistu jonosta, eventistä tai taistelusta tarvittaessa)!'
  lms-joined: '&e<player>&6 liittyi LMS eventtiin!'
  lms-left: '&a<player>&6 poistui LMS eventistä!'
  lms-death-message: '&e<player>&6 kuoli LMS eventissä! <alive> pelaajaa jäljellä!'
  lms-already-started: '&6LMS event ei ole avoin nyt!'
  lms-winner: '&e&l<player>&6&l voitti LMS eventin!'
  lms-has-not-started: '&cLMS event ei ole avoin vielä!'
  koth-winner: '&e&l<team>&6&l voitti KOTH eventin!'
  koth-join-back: '&aLiity takaisin KOTH eventtiin komennolla /koth join'
  koth-already-started: '&cTuo event on jo alkanut!'
  koth-has-not-started: '&cKOTH eventti ei ole avoin vielä.'
  koth-joined: '&e<player>&6 liittyi KOTH eventtiin!'
  koth-left: '&e<player>&6 poistui KOTH eventistä!'
  can-not-duel-that-player: '&cEt voi haastaa tuota pelaajaa nyt!'
  brackets-next-fight: '&6Seuraava brackets taistelu on &e<player1> &6VS &e<player2>!'
  brackets-slays: '&e<player1> &6tappoi pelaajan &e<player2> &9brackets eventissä!'
  brackets-winner: '&l&e<player>&6&l voitti brackets eventin!'
  brackets-joined: '&e<player>&6 liittyi brackets eventtiin!'
  brackets-left: '&e<player>&6 poistui brackets eventista!'
  brackets-already-started: '&cTuo event on jo alkanut!'
  brackets-not-started: '&cBrackets event ei ole avoin vielä!'
  language-set: '&6Sinun kielesi on nyt &e<language>&6.'
  queue-cooldown: '&cSinun pitää odottaa &l<time>&r&c ennen kuin voit liittyä jonoon uudelleen!'
  elo-queue-cooldown: '&cSinun pitää odottaa &l<time>&r&c ennen kuin voit liittyä elo jonoon uudelleen!'
  duel-requests-off: '&6Et enää saa taistelu pyyntöjä.'
  duel-requests-on: '&6Saat taistelu pyyntöjä nyt.'
  has-requests-disabled: '&c&e<player>&6 on poistanut taistelupyynnöt käytöstä!'
  playback-not-found: '&cTaistelun toistoa ei löytynyt :('
  teleporting: '&eTeleportataan <seconds> sekunnissa...'
  teleport-cancelled: '&cTeleporttaus peruutettiin, koska liikuit tai otit vahinkoa.'
  party-help:
    - '&6&LParty Tuki:'
    - '&6&l/Party create &r&e- Tee oma party'
    - '&6&l/Party invite <player> &r&e- Kutsu pelaajia partyysi'
    - '&6&l/Party join &r&e- Liity partyyn'
    - '&6&l/Party kick <player> &r&e- Potki pelaaja partystasi'
    - '&6&l/Party leave &r&e- Poistu nykyisestä partystasi'
    - '&6&l/Party disband &r&e- Lakkauta partysi'
    - '&6&l/Party info &r&e- Näytä inffoa partystasi'
    - '&6&l/Party chat &r&e- Chattaa toisten party jäsenien kanssa'
    - '&6&l/Party promote &r&e- Ylennä partysi jäsen'
    - '&6&l/Party settings &r&e- Avaa asetusvalikko'
    - '&6&l/Party fight &r&e- Taistele'
  stats:
    kills: '&6Tapot: &e<value>'
    deaths: '&6Kuolemat: &e<value>'
    elo: '&6<kit> elo: &e<value>'
    party-wins: '&6Party Voitot: &e<value>'
    brackets: '&6Brackets Voitot: &e<value>'
    lms: '&6LMS Voitot: &e<value>'
    global-elo: '&6Globaali ELO: &e<value>'
  fight-start-message:
    #leave 'false' for no message
    build: '&9Voit rakentaa tämän kitin kanssa!'
    elo: '&e<you>&6 (&e<your_elo>&6) VS &e<opponent>&6 (&e<opponent_elo>&6)'
    only-bow: '&9Voit käyttää vain jousipyssyä tässä taistelussa!'
    horse: 'false'
    combo: '&9Voit lyödä paljon nopeammin tämän kitin kanssa!'
    duel: '&91v1 pelaajaa <opponent> vastaan.'
    party-ffa: '&9Party FFA partysi pelaajia vastaan.'
    party-vs-party: '&9Party VS Party pelaajan <opponent> partya vastaan.'
    party-split: '&9Partysi jaettiin kahteen tiimiin.'
chinese:
  yes-or-true: '&6Yes'
  no-or-false: '&cNo'
  cooldown-message: '&c你不能再次使用這個物品! 你必須在等 <time>'
  do-not-spam-things: '&c不要再次發送垃圾郵件!'
  not-pvp-fight: '&c在這場戰鬥中你不能傷害其他人!'
  party-created: '&a你創建了一個派對!'
  can-not-duel-that-party: '&c不能向那個派對發起決鬥'
  playback-can-not-start: '&c由於競技場目前無法使用的，因此無法開始，請通知管理員'
  in-party: '&a你已經在派對裡了!'
  data-not-loaded: '&c&l數據尚未加載！請稍等片刻'
  ranked-range: '&6尋找排名賽對手，範圍: (&e<range1>-<range2>&6).'
  ranked-anyone: '&6搜索任何等級分範圍內的玩家'
  invalid-kit: '&c無法預覽無效的套件'
  not-own-party: '&c你不是派對創建人!'
  stay-away-from-other-players: '&c觀看時請遠離其他玩家！'
  public-party-no-permission: '&c您無權舉辦公共派對！'
  no-party-limit-bypass: '&c你沒有權限改變你隊伍的玩家限制！'
  party-full: '&c那個派對已經滿了！'
  public-party-on: '&a您現在正在舉辦公共派對！'
  public-party-off: '&a您不再舉辦公共派對！'
  public-party-broadcast: '&e<player>&6 舉辦了公共派對，點選此訊息或輸入&e/party join <player>&6來加入!'
  want-to-invite: '&e<player>&6邀請你參加他的派對，輸入&e/party join <player>&6來加入'
  sent-invite: '&6你已經發送邀請!'
  stats-message: '&e<player>&6的狀態'
  #調成'false'來取消
  started-spectating: '&e<player> &6正在觀看你的戰鬥!'
  stopped-spectating: '&e<player> &6不再觀看你的戰鬥!'
  #<player>, <hearts>, <hp>
  bow-health-message: '&e<player>&6還剩下 &e<hearts>&c滴血!'
  invalid-inventory: '&c找不到物品！'
  no-enough-money: '&c你沒有足夠的錢來做到這個！你至少需要$<needed>.'
  leave-spectator-mode: '&c你現在不能使用這個命令！ &6輸入&e/leave &6來離開觀戰模式!'
  party-joined: '&6&e<player>&6加入了派對!'
  inventory-message: '&6戰鬥玩家物品欄(點擊名稱): '
  event-stopped: '&c&l活動已經結束!'
  juggernaut-not-open: '&c劍聖活動現在尚未公開!'
  juggernaut-not-started: '&6劍聖活動尚未開始! 添加到劍聖隊列'
  juggernaut-eliminated: '&c&l劍聖被&a<killer>&c&l淘汰了!'
  juggernaut-eliminated-logging-out: '&c&l劍聖因退出而被淘汰了！'
  juggernaut-join-back: '&a再次加入劍聖活動使用指令/juggernaut join'
  player-is-juggernaut: '&c&l玩家 &a&l<player>&c&l已經成為劍聖了!'
  party-chat-join: '&a你現在在派對聊天室!'
  party-chat-leave: '&a你現在在公共聊天室!'
  no-permission: '&c你沒有權限去做那件事!'
  does-not-have-party: '&6該玩家不能再參加派對了!'
  party-wants-duel: '&e<player>&6發送了''<kit>''派對vs派對的請求,輸入 &e/party duel <player>&6接受'
  party-wants-duel-with-custom-kit: '&e<player>&6 已發送定制套件(''<kit>'') 派對vs派對，輸入&e/duel duel <player>&6接受'
  not-online: '&c那位玩家不在線上!'
  has-not-played: '&c那個玩家還沒有加入伺服器！'
  not-enough-players: '&c你的派對必須至少有2名玩家才能做到這點'
  can-not-do-while-in-match: '&c如果您正在進行決鬥或活動，則不能這樣做！'
  can-not-do-while-in-party-other-player: '&c如果該玩家參加派對，你就不能這樣做！'
  can-not-do-while-in-party: '&c如果你參加派對，你不能這樣做！'
  has-not-invited: '&c沒有人邀請你'
  party-has-not-dueled: '&c該派對尚未發送派對邀請或請求已過期'
  showing-stats: '&6顯示&e<player>&6的統計數據!'
  not-in-your-party: '&c那位玩家不在你的隊伍中!'
  was-kicked: '&c&e<player>&6被踢出派對!'
  left-party: '&c&e<player>&6離開了派對!'
  alread-invited: '&c那位玩家已經被邀請了'
  disband-to-leave: '&c你是這個派對的創建者，所以你必須解散派對或促使一個成員離開黨'
  party-was-deleted: '&c派對已被解散!'
  can-not-do-party-commands: '&c你現在不能使用派對指令!'
  can-not-do-command: '&c你現在不能使用那個指令!'
  no-arenas-found: '&c現在沒有可用的地圖，請通知管理員!'
  request-sent: '&6你發送了一個戰鬥邀請!'
  wants-duel: '&c&e<player>&6發送了一個 ''<kit>''的決鬥邀請,輸入&e/duel accept <player>&6接受.'
  bestof-duel-request: '&c決鬥最好的一輪 <rounds>!'
  wants-duel-with-custom-kit: '&c&e<player>&6 發送訂製套件(''<kit>'')決鬥的邀請,輸入&e/duel accept <player>&6接受'
  you-can-not-duel-now: '&c你現在不能決鬥!'
  waiting-for-duel: '&6正在為你尋找對手... 模式: <kit>'
  best-of-round: '&a最好的<total_rounds> 回合 Round: <round> &a勝利: &6<player1>: &e<player1_wins> &6: <player2>: &e<player2_wins>'
  queue-ping-limit: '&c你不允許加入排名隊列，因為延遲太高(<ping> ms)'
  no-rankeds-left: '&c你今天沒有剩下任何排名賽'
  no-unrankeds-left: '&c你今天沒有任何非排名的比賽'
  no-premium-matches-left: '&c你還沒有任何高級比賽'
  ranked-kills-required: '&c你必須還要殺死<required>人，才能加入排名隊列 你已經有<kills>擊殺了 '
  has-not-dueled: '&c該玩家沒有給你或者請求已經過期因此決鬥無法啟動！'
  left-queue: '&c你離開了你的列隊!'
  type-name: '請在這裡 輸入他們的 名字'
  kit-saved: '&3&l你的排版已經儲存!'
  kit-reset: '&3&l你的排版已經重置!'
  party-promoted: '&e&l<player>&6&l在你的派對中得到晉升!'
  your-team-won: '&6&l你的隊伍贏了!'
  your-team-won-disband: '&c你的隊伍贏了，因為敵人解散了派對!'
  your-team-did-not-win: '&c&l你的隊伍輸了!'
  in-fight: '&c你或你的隊友還在遊戲中'
  not-in-fight: '&c玩家必須參加比賽'
  #This message is sent when the fight has 20% of max duration left
  #for example if the limit is 20 minutes this message is sent every minute for (20*0.2 = 4) 4 minutes before the match ends
  fight-duration-limit: '&c戰鬥持續時間<limit>分鐘，比賽還剩<left>分鐘'
  #sent if the fight exceeds the fight-duration-limit
  fight-duration-force-end: '&c戰鬥超過戰鬥限制時間限制並被停止!'
  ffa-winner: '&e<player>&6 在你的派對中贏得了派對FFA!'
  your-member-died: '&c&e<player>&6死了！你的隊伍還剩下<alive>位玩家'
  ffa-died: '&c&e<player>&6死了！還有<alive>位玩家活著'
  opponent-member-died: '&6&e<player>&6死亡!敵隊還有<alive>位玩家活著'
  can-not-teleport: '&c你不能在比賽中傳送!'
  countdown-message: '&6戰鬥在&e&l<seconds>&r&6秒後開始!'
  countdown-go: '&6戰鬥開始了，和你的對手戰鬥吧!(&c請勿蓋空中堡壘)'
  ffa-arena-reset: '&c&lFFA地圖將在<time>秒後重置!'
  #<elo1> and <elo2> for current elos
  #<old1> and <old2> for old elo
  #<diff1> and <diff2> for elo changes
  elo-fight: '&6Elo changes: <player1> <old1>(+<diff1>) & <player2> <old2>(<diff2>)'
  rank-message: '&c&l你的排名現在是<rank>!'
  you-won: '&6&l你贏了這場戰鬥!'
  did-not-win: '&c&l你在這場戰鬥中輸了.'
  blocked-command: '&c您現在可以立即執行該命令（如果需要，請保留當前的隊列，事件或匹配）!'
  lms-joined: '&e<player>&6加入了LMS 事件!'
  lms-left: '&e<player>&6離開了LMS 事件!'
  lms-death-message: '&e<player>&6 has died in the LMS event! <alive> players left!'
  lms-already-started: '&cThe LMS event is not open now!'
  lms-winner: '&e&l<player>&6&l has won the LMS event!'
  lms-has-not-started: '&cThe LMS event is not open yet.'
  koth-winner: '&e&l<team>&6&l has won the KOTH event!'
  koth-join-back: '&aJoin the KOTH event again with /koth join'
  koth-already-started: '&cThe event has already started!'
  koth-has-not-started: '&cThe KOTH event is not open yet.'
  koth-joined: '&e<player>&6 joined the KOTH event!'
  koth-left: '&e<player>&6 left the KOTH event!'
  can-not-duel-that-player: '&你現在不能和那個玩家決鬥!'
  brackets-next-fight: '&6下一場戰鬥: &e<player1> &6VS &e<player2>!'
  brackets-slays: '&e<player1> &6擊殺了 &e<player2> &6在括號事件!'
  brackets-winner: '&e&l<player>&6 has won the brackets event!'
  brackets-joined: '&e<player>&6 has joined the brackets event!'
  brackets-left: '&e<player>&6 has left the brackets event!'
  brackets-already-started: '&cThe event has already started!'
  brackets-not-started: '&cThe brackets event is not open yet!'
  sumo-next-fight: '&6下一場sumo戰鬥是由 &e<player1> &6VS &e<player2>!'
  sumo-slays: '&e<player1> &6擊殺了 &e<player2> &6在sumo事件!'
  sumo-winner: '&e&l<player>&6贏了sumo戰!'
  sumo-joined: '&e<player>&6加入了sumo!'
  sumo-left: '&e<player>&6 離開sumo活動!'
  sumo-already-started: '&csumo活動已經開始!'
  sumo-not-started: '&c相撲活動尚未公開!'
  language-set: '&6你的語言已變更為&e<language>&6.'
  queue-cooldown: '&c你必須再等&l<time>秒 &r&c才能再次加入列隊!'
  elo-queue-cooldown: '&c你必須再等待 &l<time>秒(s) &r&c才能再次加入elo列隊!'
  duel-requests-off: '&6你將不再獲得決鬥請求'
  duel-requests-on: '&6你現在將得到決鬥請求'
  has-requests-disabled: '&c&e<player>&6已經拒絕收到決鬥請求!'
  playback-not-found: '&c很抱歉，我們找不到你的戰鬥回放 :('
  kill-cam: '&b點擊此處重播戰鬥的最後幾秒'
  teleporting: '&e將在<seconds>秒後傳送...'
  teleport-cancelled: '&c傳送被取消因為你受傷或移動'
  party-help:
    - '&6&lParty Help:'
    - '&6&l/Party create &r&e- 創建一個派對'
    - '&6&l/Party invite <player> &r&e- 邀請你的朋友進到派對'
    - '&6&l/Party join &r&e- 加入一個派對'
    - '&6&l/Party kick <player> &r&e- 將成員踢出派對'
    - '&6&l/Party leave &r&e- 離開你的派對'
    - '&6&l/Party disband &r&e- 解散你的派對'
    - '&6&l/Party info &r&e- 顯示派對信息'
    - '&6&l/Party chat &r&e- 向派對成員講話'
    - '&6&l/Party promote &r&e- 公開至公共頻道邀請'
    - '&6&l/Party settings &r&e- 打開設定'
    - '&6&l/Party fight &r&e- Fight'
  stats:
    kills: '&6擊殺數: &e<value>'
    deaths: '&6死亡數: &e<value>'
    elo: '&6<kit> elo: &e<value>'
    party-wins: '&6派對勝利: &e<value>'
    brackets: '&6Brackets Wins: &e<value>'
    lms: '&6LMS Wins: &e<value>'
    global-elo: '&6通用 ELO: &e<value>'
  fight-start-message:
    #leave 'false' for no message
    build: '&9你可以在這模式放置方塊!'
    elo: '&e<you>&6 (&e<your_elo>&6) VS &e<opponent>&6 (&e<opponent_elo>&6)'
    only-bow: '&9你只能在這個模式使用箭!'
    horse: 'false'
    combo: '&9你可以在這模式快速傷害!'
    duel: '&91v1 against <opponent>'
    party-ffa: '&9Party FFA against members in your party.'
    party-vs-party: '&9Party VS Party againts <opponent>.'
    party-split: '&9Your party was split in two teams.'
italiano:
  yes-or-true: '&6Si'
  no-or-false: '&cNo'
  cooldown-message: '&cAspetta prima di rifare questa azione! Devi aspettare <time> secondi'
  do-not-spam-things: '&cNon cliccare troppo velocemente!'
  not-pvp-fight: '&cNon puoi colpire gli altri in questo duello!'
  party-created: '&aHai creato un nuovo party!'
  can-not-duel-that-party: '&cNon puoi duellare questo party.'
  playback-can-not-start: '&cNon puoi guardare questo replay perchè la arena è piena.'
  in-party: '&aSei già in un party!'
  data-not-loaded: '&c&lI dati non hanno ancora caricato! Perfavore, aspetta un secondo.'
  ranked-range: '&6Cercando dei giocatori nel tuo raggio di elo (&e<range1>-<range2>&6).'
  ranked-anyone: '&6Cercando dei giocatori con un elo qualunque.'
  invalid-kit: '&cNon puoi ricevere un anteprima di un kit invalido!'
  not-own-party: '&cNon sei il proprietario di nessun party!'
  party-size: '&cParty must have <number> players!'
  stay-away-from-other-players: '&cSi prega di stare lontano da altri giocatori mentre stai guardando!'
  public-party-no-permission: '&cNon hai il permesso di ospitare party pubblici!'
  no-party-limit-bypass: '&cNon hai il permesso di cambiare il numero di giocatori nel party!'
  party-full: '&cQuel party è già pieno!'
  public-party-on: '&aOra il tuo party è pubblico!'
  public-party-off: '&aNon stai più ospitando un party pubblico!'
  public-party-broadcast: '&e<player>&6 sta ospitando un party pubblico! Clicca questo messaggio o scrivi &e/party join <player>&6 per entrare!'
  want-to-invite: '&e<player>&6 ti ha invitato nel tuo party, scrivi &e/party join <player>&6.'
  sent-invite: '&6Hai inviato un invito!'
  stats-message: '&6Statistiche di &e<player>'
  #set to 'false' to disable
  started-spectating: '&e<player> &6sta osservando la tua partita!'
  stopped-spectating: '&e<player> &6non sta più osservando la tua partita!'
  #<player>, <hearts>, <hp>
  bow-health-message: '&e<player> &6ha &e<hearts>&6!'
  invalid-inventory: '&cL''inventario non è stato trovato!'
  no-enough-money: '&cNon hai abbastanza soldi per farlo! hai bisogno di almeno $<needed>.'
  leave-spectator-mode: '&cNon puoi fare questo comando ora! &6Scrivi &e/leave &6per uscire dalla modalità spettatore!'
  party-joined: '&6&e<player>&6 è entrato nel party!'
  inventory-message: '&6Inventari (click): '
  event-stopped: '&c&lL''evento è terminato!'
  juggernaut-not-open: '&cL''evento juggernaut non è aperto ora!'
  juggernaut-not-started: '&6L''evento Juggernaut non è ancora iniziato! Aggiunto alla coda juggernaut.'
  juggernaut-eliminated: '&c&lIl Juggernaut è stato eliminato da &a<killer>&c&l!'
  juggernaut-eliminated-logging-out: '&c&lIl juggernaut è stato eliminato per essere uscito!'
  juggernaut-join-back: '&aPartecipa di nuovo all''evento Juggernaut con /juggernaut join'
  juggernaut-chance: '&6Hai la tua possibilità di essere il Juggernaut!'
  player-is-juggernaut: '&c&lIl giocatore &a&l<player>&c&l è ora il juggernaut!'
  party-chat-join: '&aOra stai parlando nella chat del party!'
  party-chat-leave: '&aOra stai parlando nella chat pubblica!'
  no-permission: '&cNon hai il permesso di farlo!'
  does-not-have-party: '&6Quel giocatore non ha più un party!'
  party-wants-duel: '&e<player>&6 ha inviato una richiesta di duello party vs party con il kit ''<kit>&r&6''.'
  party-wants-duel-with-custom-kit: '&e<player>&6 has sent a custom kit (''<kit>&r&6'') party vs party request.'
  not-online: '&cIl giocatore non è più online!'
  has-not-played: '&cQuel giocatore non si è unito al server!'
  not-enough-players: '&cIl party richiede almeno due giocatori per giocare.'
  can-not-do-while-in-match: '&cNon puoi farlo se sei in un duello o evento!'
  can-not-do-while-in-party-other-player: '&cNon puoi farlo se quel giocatore è in un party!'
  can-not-do-while-in-party: '&cNon puoi farlo se quel giocatore è in un party!'
  has-not-invited: '&cNessuno ti ha invitato.'
  party-has-not-dueled: '&cQuesto party non ti ha invitato ad un duello oppuure la richiesta è scaduta.'
  showing-stats: '&6Controllando le statistiche di &e<player>!'
  not-in-your-party: '&cQuesto giocatore non è nel tuo party!'
  was-kicked: '&c&e<player>&6 è stato kickato dal party!'
  left-party: '&c&e<player>&6 è uscito dal party!'
  alread-invited: '&cQuesto giocatore è già stato invitato al party.'
  disband-to-leave: '&cSei il proprietario di questo party, quindi devi eliminare il party oppure promuovere un membro per poter uscire dal party.'
  party-was-deleted: '&cQuesto party è stato eliminato!'
  can-not-do-party-commands: '&cNon puoi eseguire i comandi del party ora!'
  can-not-do-command: '&cNon puoi fare questo comando ora!'
  no-arenas-found: '&cNon ci sono arene libere. Riprova presto!'
  request-sent: '&6Hai inviato una richiesta di duello a &e<target> &6con il kit &e<kit> nella arena &e<arena>&6!'
  wants-duel: '&c&e<player>&6 ha inviato una richiesta di duello con il kit <kit>&r&6.'
  bestof-duel-request: '&cIl duello è al meglio di <rounds> round!'
  wants-duel-with-custom-kit: '&c&e<player>&6 ha inviato un kit personalizzato (''<kit>&r&6'')&6 richiesta di duello.'
  you-can-not-duel-now: '&cNon puoi duellare con nessuno ora!'
  waiting-for-duel: '&6In attesa di un avversario nella coda del kit <kit>'
  best-of-round: '&aAl meglio di <total_rounds> round. Round: <round>. &aVittorie: &6<player1>: &e<player1_wins> &6: <player2>: &e<player2_wins>'
  queue-ping-limit: '&cNon ti è permesso di partecipare alla coda classificata perché il tuo ping è troppo alto (<ping> ms)'
  no-rankeds-left: '&cNon hai più partite classificate per oggi.'
  no-unrankeds-left: '&cYou don''t have any unranked matches left for today.'
  no-premium-matches-left: '&cYou don''t have any premium matches left.'
  ranked-kills-required: '&cDevi avere vinto almeno <required> partite prima di poter entrare nella coda classificata. Ora hai <kills> vittorie.'
  has-not-dueled: '&cQuel giocatore non ti ha duellato o la richiesta è scaduta o il duello non può essere iniziato!'
  left-queue: '&cHai lasciato la coda!'
  type-name: 'Inserisci il loro nome qui.'
  kit-saved: '&3&lIl tuo kit è stato salvato!'
  kit-reset: '&3&lIl tuo kit è stato ripristinato!'
  party-promoted: '&e&l<player>&6&l è stato promosso nel party!'
  your-team-won: '&6&lLa tua squadra ha vinto!'
  your-team-won-disband: '&cLa tua squadra ha vinto, perché l''avversario ha sciolto il suo party!'
  your-team-did-not-win: '&c&lLa tua squadra ha perso il fight!'
  in-fight: '&cO la tua o la loro squadra è ancora in fight.'
  not-in-fight: '&cIl giocatore deve essere in un fight.'
  #This message is sent when the fight has 20% of max duration left
  #for example if the limit is 20 minutes this message is sent every minute for (20*0.2 = 4) 4 minutes before the match ends
  fight-duration-limit: '&cLa durata del fight è limitata a <limit> minuti. <left> minuti rimasti della partita.'
  #sent if the fight exceeds the fight-duration-limit
  fight-duration-force-end: '&cIl fight ha superato il limite di durata ed è stato interrotto.'
  ffa-winner: '&e<player>&6 ha vinto il fight!'
  your-member-died: '&c&e<player>&6 è morto! Il tuo team ha <alive> giocatori.'
  ffa-died: '&c&e<player>&6 è morto! Rimangono <alive> giocatori.'
  opponent-member-died: '&6&e<player>&6 è morto! Il team avversario ha <alive> giocatori.'
  can-not-teleport: '&cNon puoi teletrasportarti mentre sei in una partita!'
  countdown-message: '&6La tua partita inizierà in &e&l<seconds>&r&6 secondi!'
  countdown-go: '&cVAI!'
  ffa-arena-reset: '&c&lLe arene ffa si ripristineranno in <time> secondi!'
  #<elo1> and <elo2> for current elos
  #<old1> and <old2> for old elo
  #<diff1> and <diff2> for elo changes
  elo-fight: '&6Cambiamenti di ELO: <player1> <old1>(+<diff1>) & <player2> <old2>(<diff2>)'
  rank-message: '&c&lIl tuo rank è ora <rank>!'
  you-won: '&6&lHai vinto!'
  did-not-win: '&c&lHai perso.'
  blocked-command: '&cOra ti è permesso di eseguire quel comando (lasciare la coda, l''evento o la corrispondenza attuali se necessario)!'
  lms-joined: '&e<player>&6 è entrato nell''evento LMS!'
  lms-left: '&e<player>&6 è uscito dall''evento LMS!'
  lms-death-message: '&e<player>&6 è morto! <alive> giocatori rimasti!'
  lms-already-started: '&cL''evento LMS è già iniziato!'
  lms-winner: '&e&l<player>&6&l ha vinto l''evento LMS!'
  lms-has-not-started: '&cL''evento LMS non è ancora iniziato.'
  koth-winner: '&e&l<team>&6&l ha vinto l''evento KOTH!'
  koth-join-back: '&aPartecipa nuovamente all''evento KOTH con /koth join'
  koth-already-started: '&cL''evento è già iniziato!'
  koth-has-not-started: '&cL''evento KOTH non è ancora aperto.'
  koth-joined: '&e<player>&6 è entrato nell''evento KOTH!'
  koth-left: '&e<player>&6 ha lasciato l''evento KOTH!'
  can-not-duel-that-player: '&cNon puoi duellare quel giocatore ora!'
  brackets-next-fight: '&6Il fight successivo sarà tra &e<player1> &6VS &e<player2>!'
  brackets-slays: '&e<player1> &6distrugge &e<player2> &6nel torneo!'
  brackets-winner: '&e&l<player>&6 ha vinto il torneo!'
  brackets-joined: '&e<player>&6 è entrato nel torneo!'
  brackets-left: '&e<player>&6 è uscito dal torneo!'
  brackets-already-started: '&cL''evento è già iniziato!'
  brackets-not-started: '&cIl torneo non è ancora iniziato!'
  sumo-next-fight: '&6La prossima lotta di sumo è tra &e<player1> &6VS &e<player2>!'
  sumo-slays: '&e<player1> &6butta giù &e<player2> &6nell''evento sumo!'
  sumo-winner: '&e&l<player>&6 ha vinto l''evento sumo!'
  sumo-joined: '&e<player>&6 è entrato nell''evento sumo!'
  sumo-left: '&e<player>&6 ha lasciato l''evento sumo!'
  sumo-already-started: '&cL''evento è già iniziato!'
  sumo-not-started: '&cL''evento sumo non è ancora aperto!'
  language-set: '&6La tua lingua è ora &e<language>&6.'
  queue-cooldown: '&cDevi aspettare &l<time> secondi &r&cprima di poter tornare in coda!'
  elo-queue-cooldown: '&cDevi aspettare &l<time> secondi &r&cprima di poter tornare in coda!'
  duel-requests-off: '&6Non riceverai più richieste di duello.'
  duel-requests-on: '&6Riceverai richieste di duello.'
  has-requests-disabled: '&c&e<player>&6 ha disabilitato le richieste di duello!'
  playback-not-found: '&cIl replay non può essere trovato, ci dispiace.'
  kill-cam: '&bClicca per vedere il replay degli ultimi secondi del fight.'
  teleporting: '&eStai venendo teletrasportato a <seconds> secondi...'
  teleport-cancelled: '&cIl teletrasporto è stato annullato perché hai preso danni o ti sei spostato'
  party-help:
    - '&7----------------&2Aiuto &aParty&7----------------'
    - '&6&l/Party create &r&e- Crea un party'
    - '&6&l/Party invite <giocatore> &r&e- Invita un amico al tuo party'
    - '&6&l/Party join &r&e- Entra in un party'
    - '&6&l/Party kick <giocatore> &r&e- Espelli un membro del party'
    - '&6&l/Party leave &r&e- Esci dal party'
    - '&6&l/Party disband &r&e- Elimina il tuo party'
    - '&6&l/Party info &r&e- Controlla delle informazioni sul tuo party'
    - '&6&l/Party chat &r&e- Scrivi nella chat del party'
    - '&6&l/Party promote &r&e- Promuovi un membro'
    - '&6&l/Party settings &r&e- Apri il menu impostazioni'
    - '&6&l/Party fight &r&e- Combatti'
    - '&6&l/Party list &r&e- Ottieni una lista dei party del server'
  stats:
    kills: '&6Uccisioni: &e<value>'
    deaths: '&6Morti: &e<value>'
    elo: '&6<kit> elo: &e<value>'
    party-wins: '&6Vittorie Party: &e<value>'
    brackets: '&6Vittorie Tornei: &e<value>'
    lms: '&6Vittorie LMS: &e<value>'
    global-elo: '&6ELO Globale: &e<value>'
  fight-start-message:
    #leave 'false' for no message
    build: 'false'
    elo: '&e<you>&6 (&e<your_elo>&6) CONTRO &e<opponent>&6 (&e<opponent_elo>&6)'
    only-bow: '&9Puoi solo usare l'' arco in questo kit!'
    horse: 'false'
    combo: 'false'
    duel: '&91v1 contro <opponent>'
    party-ffa: '&9Party Tutti Contro Tutti.'
    party-vs-party: '&9Party VS Party contro <opponent>.'
    party-split: '&9Il tuo party è stato diviso in due squardre.'
